# 心晴冥想应用 - 项目交接文档

## 📋 项目概述

**项目名称**: 心晴冥想 (relax_meditation)  
**项目类型**: Flutter跨平台移动应用  
**版本**: 1.3.3+**********  
**支持平台**: iOS、Android、HarmonyOS  
**主要功能**: 冥想放松、音频引导、计划管理、AI聊天、用户成长记录

## 🏗️ 技术架构

### 核心技术栈
- **开发框架**: Flutter (SDK >=3.0.0 <4.0.0)
- **编程语言**: Dart
- **状态管理**: GetX + Provider
- **网络请求**: Dio + 缓存拦截器
- **音频播放**: just_audio + just_audio_background
- **UI适配**: flutter_screenutil
- **本地存储**: shared_preferences

### 项目架构模式
采用基于GetX的MVC架构：
- **Model**: 数据模型 (`lib/app/core/model/`)
- **View**: UI界面 (`lib/app/modules/*/views/`)
- **Controller**: 业务逻辑 (`lib/app/modules/*/controllers/`)

## 📁 项目结构

```
lib/
├── main.dart                    # 应用入口
├── app/
│   ├── bindings/               # GetX依赖注入绑定
│   ├── core/                   # 核心组件
│   │   ├── base/              # 基础类(BaseController, BaseView)
│   │   ├── common/            # 公共组件(AppInit, EventBus等)
│   │   ├── config/            # 配置文件
│   │   ├── helper/            # 辅助类(权限、支付、推送等)
│   │   ├── model/             # 数据模型
│   │   ├── services/          # 全局服务(UserService, QuestionService)
│   │   ├── utils/             # 工具类
│   │   ├── values/            # 常量定义
│   │   └── widgets/           # 通用UI组件
│   ├── data/                  # 数据层
│   ├── modules/               # 功能模块
│   │   ├── aiChat/           # AI聊天模块
│   │   ├── home/             # 首页模块
│   │   ├── meditation/       # 冥想模块
│   │   ├── mine/             # 个人中心
│   │   ├── plans/            # 计划管理
│   │   ├── player/           # 音视频播放器
│   │   ├── questionnaire/    # 问卷调查
│   │   ├── splash/           # 启动页
│   │   └── user/             # 用户模块
│   ├── network/              # 网络层
│   └── routes/               # 路由配置
├── buildConfig/              # 构建配置
└── channel/                  # 原生通道
```

## 🚀 快速开始

### 环境要求
- Flutter SDK: >=3.0.0
- Dart SDK: >=3.0.0
- Android Studio / VS Code
- Xcode (iOS开发)

### 安装步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd relax_meditation
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **配置环境**
   - 开发环境: `env.dev` (API: https://relaxed-mirror.navolove.com/api/)
   - 生产环境: `env.prod` (API: https://relaxed.navolove.com/api/)

4. **运行项目**
   ```bash
   # 开发环境
   flutter run --dart-define=ENVIRONMENT=dev
   
   # 生产环境
   flutter run --dart-define=ENVIRONMENT=prod
   ```

## 🔧 核心功能模块详解

### 1. 用户模块 (user)
**位置**: `lib/app/modules/user/`

**主要功能**:
- 手机号登录 (`login_controller.dart`)
- 验证码验证 (`verification_code_controller.dart`)
- 一键登录 (`shanYanHelper.dart`)
- 微信登录 (`fluwx_helper.dart`)

**关键文件**:
- `controllers/login_controller.dart`: 登录逻辑
- `controllers/verification_code_controller.dart`: 验证码处理
- `views/login_view.dart`: 登录界面

**代码示例**:
```dart
// 登录请求示例
requestLogin(String code) async {
  Loading.esayLoadingText('登录中...');
  var appResponse = await post<UserEntity, UserEntity>(
    Api.apiLogin,
    data: {
      'type': 1,
      'username': phoneNumber.value,
      'code': code
    },
    decodeType: UserEntity(),
  );
  appResponse.when(
    success: (userEntity) {
      UserService.to.handleLoginSuccess(userEntity);
      Get.back(result: true);
    },
    failure: (msg, code) {
      // 错误处理
    }
  );
}
```

### 2. 冥想模块 (meditation)
**位置**: `lib/app/modules/meditation/`

**主要功能**:
- 冥想课程分类展示
- 课程详情页面
- 课程收藏功能
- 播放记录统计

**关键文件**:
- `controllers/meditation_controller.dart`: 分类管理
- `controllers/course_info_controller.dart`: 课程详情
- `views/meditation_view.dart`: 课程列表界面

### 3. 音频播放器 (player)
**位置**: `lib/app/modules/player/`

**主要功能**:
- 音频播放控制
- 播放列表管理
- 循环模式切换
- 定时关闭功能
- 后台播放支持

**关键文件**:
- `controllers/audio_player_controller.dart`: 播放器核心逻辑
- `views/audio_player_view.dart`: 播放器界面

**播放模式**:
- 单章播放 (LoopMode.off)
- 单曲循环 (LoopMode.one)
- 顺序播放 (LoopMode.off)
- 列表循环 (LoopMode.all)

**代码示例**:
```dart
// 播放器初始化
Future<void> play() async {
  if (_playlist.isEmpty) return;
  await getCacheUrl(); // 获取缓存URL
  await _player.setAudioSource(
    ConcatenatingAudioSource(
      useLazyPreparation: true,
      children: _playlist.map((course) {
        return AudioSource.uri(
          Uri.parse(course.playUrl!),
          tag: MediaItem(
            id: '${course.id}',
            title: '${course.title}',
            artUri: Uri.parse(course.thumbImg!),
          ),
        );
      }).toList(),
    ),
    initialIndex: _currentIndex,
  );
  await _player.play();
}

// 播放模式切换
void changePlayMode() {
  playMode++;
  if (courseType == 'subcat') {
    if (playMode > 3) playMode = 0;
  } else if (courseType == 'singles') {
    if (playMode > 1) playMode = 0;
  }

  switch (playMode) {
    case 0: setLoopMode(LoopMode.off); break;    // 单章播放
    case 1: setLoopMode(LoopMode.one); break;    // 单曲循环
    case 2: setLoopMode(LoopMode.off); break;    // 顺序播放
    case 3: setLoopMode(LoopMode.all); break;    // 列表循环
  }
}
```

### 4. AI聊天模块 (aiChat)
**位置**: `lib/app/modules/aiChat/`

**主要功能**:
- AI对话交互
- 流式消息接收
- 会话记录管理
- 使用次数统计

**关键文件**:
- `controllers/ai_chat_controller.dart`: 聊天逻辑
- `helper/ai_chat_helper/`: AI服务封装

**代码示例**:
```dart
// AI聊天发送消息
void sendMessage() async {
  if (textController.text.trim().isEmpty) return;

  final userMessage = ChatMessage(
    content: textController.text,
    isAI: false,
  );
  messages.add(userMessage);

  final aiMessage = ChatMessage(
    content: '',
    isAI: true,
    status: MessageStatus.sent.value
  );
  messages.add(aiMessage);

  try {
    final stream = await aiChatService.chatWithAI(
      userMessage.content,
      chatToken.value,
      botId.value,
      conversationId: conversationId.value,
    );

    // 监听流式响应
    _streamSubscription = stream.listen(
      (chunk) {
        messages.last.content += chunk;
        messages.refresh();
      },
      onDone: () {
        messages.last.status = MessageStatus.completed.value;
        messages.refresh();
        reportChatCount(); // 上报使用次数
      },
    );
  } catch (e) {
    messages.last.status = MessageStatus.failed.value;
    messages.last.content = '网络异常，请稍后重试';
  }
}
```

### 5. 计划管理 (plans)
**位置**: `lib/app/modules/plans/`

**主要功能**:
- 个人计划创建
- 计划执行记录
- 进度统计分析

### 6. 首页模块 (home)
**位置**: `lib/app/modules/home/<USER>

**主要功能**:
- 个人数据报告
- 任务记录管理
- 快捷功能入口

## 🌐 网络请求

### API配置
- **基础URL**: 通过环境变量配置
- **请求封装**: 基于Dio的统一封装
- **错误处理**: 统一错误拦截和提示
- **缓存策略**: dio_cache_interceptor实现

### 主要API接口
- 用户登录: `Api.apiLogin`
- 冥想分类: `Api.apiGetMeditationCategory`
- 课程收藏: `Api.apiCollectMeditation`
- AI聊天Token: `Api.apiGetChatToken`

## 💾 数据存储

### 本地存储
- **SharedPreferences**: 用户信息、设置项
- **文件系统**: 媒体缓存、下载文件
- **存储工具**: `SpUtil` 封装的存储工具类

### 关键存储项
- 用户信息: `Appkeys.keyUserInfo`
- 登录Token: `Appkeys.keyToken`
- 短信初始化状态: `Appkeys.keySmsInitSuccess`

## 🔐 第三方集成

### 1. 微信SDK (fluwx)
- **App ID**: wx37cf1a55ab503d52
- **功能**: 登录、分享
- **Universal Link**: https://relaxed.navolove.com
- **配置**: `pubspec.yaml` 中的 fluwx 配置

**配置示例**:
```yaml
fluwx:
  app_id: 'wx37cf1a55ab503d52'
  debug_logging: true
  ios:
    universal_link: https://relaxed.navolove.com
    no_pay: true
```

### 2. 一键登录 (shanyan)
- **iOS App ID**: 2qdqXuZn
- **Android App ID**: O7RJ2ary
- **位置**: `plugins/shanyan/`

**使用示例**:
```dart
// 初始化闪验SDK
ShanyanHelper().init();

// 拉起授权页
ShanyanHelper().openLoginAuthPlatformState();
```

### 3. 推送服务 (mobpush)
- **位置**: `plugins/mobpush_plugin/`
- **功能**: 消息推送、别名设置

**使用示例**:
```dart
// 设置用户别名
MobPushHelper().setAlias(userId);

// 推送权限申请
MobPushHelper().requestPushPermission();
```

### 4. 数据统计 (sensors_analytics)
- **功能**: 用户行为统计
- **事件**: 登录、页面曝光、功能点击

**使用示例**:
```dart
// 用户登录统计
SensorsAnalyticsHelper.login(userId);

// 自定义事件统计
SensorsAnalyticsHelper.track(SensorEventConstant.menuClick, {
  'menu_name': '冥想',
  'page_source': '首页'
});
```

### 5. 应用内购买 (in_app_purchase)
- **支持**: iOS App Store、Google Play
- **管理**: `InAppPurchaseManager`

**使用示例**:
```dart
// 查询商品信息
await InAppPurchaseManager.queryProductDetails(['product_id']);

// 发起购买
await InAppPurchaseManager.buyProduct(productDetails);
```

## 🎯 关键业务流程

### 用户登录流程
1. 用户输入手机号
2. 获取验证码 / 一键登录
3. 验证成功后调用登录API
4. 保存用户信息和Token
5. 设置推送别名
6. 发送登录成功事件

### 音频播放流程
1. 获取播放列表
2. 缓存音频URL
3. 设置AudioSource
4. 开始播放
5. 监听播放状态
6. 更新UI界面

### AI聊天流程
1. 获取聊天Token
2. 发送用户消息
3. 建立流式连接
4. 接收AI回复
5. 更新消息列表
6. 统计使用次数

## 🛠️ 开发工具和调试

### 调试工具
- **DoKit**: 开发调试面板 (`flutter_dokit_plugin`)
- **Logger**: 日志输出
- **Pretty Dio Logger**: 网络请求日志

### 性能监控
- **神策统计**: 用户行为分析
- **错误监控**: 异常捕获和上报

## 📱 平台特性

### iOS特性
- 后台音频播放
- 快捷操作 (Quick Actions)
- 应用内购买
- 隐私权限管理

### Android特性
- 渠道包管理 (walle_kit)
- 权限动态申请
- 后台保活
- 通知管理

### HarmonyOS支持
- 基础功能适配
- 构建配置: `ohos/` 目录

## 🔧 常见问题和解决方案

### 1. 编译问题
- **依赖冲突**: 运行 `flutter pub deps` 检查依赖树
- **版本不兼容**: 检查 `pubspec.yaml` 中的版本约束
- **缓存问题**: 运行 `flutter clean && flutter pub get`
- **Gradle问题**: 检查 `android/gradle.properties` 配置
- **CocoaPods问题**: 在 `ios/` 目录运行 `pod install`

### 2. 网络请求问题
- **环境配置**: 检查 `env.dev` 和 `env.prod` 文件
- **证书问题**: 确认HTTPS证书有效性
- **代理设置**: 检查网络代理配置
- **超时问题**: 调整Dio的超时配置
- **Token过期**: 检查用户登录状态

### 3. 音频播放问题
- **权限问题**: 确认音频播放权限
- **格式支持**: 检查音频文件格式 (支持MP3、AAC等)
- **缓存问题**: 清理音频缓存目录
- **后台播放**: 检查后台播放权限配置
- **耳机适配**: 测试有线/无线耳机兼容性

### 4. 第三方SDK问题
- **微信SDK**: 检查App ID和签名配置
- **一键登录**: 确认运营商网络环境
- **推送服务**: 检查推送配置和证书
- **支付问题**: 验证支付配置和商户信息
- **统计上报**: 检查神策SDK初始化

### 5. 平台特定问题

#### iOS问题
- **证书过期**: 更新开发者证书
- **权限描述**: 检查 `Info.plist` 权限描述
- **后台模式**: 确认后台音频播放配置
- **App Store审核**: 遵循苹果审核指南

#### Android问题
- **权限申请**: 检查动态权限申请
- **渠道包**: 验证walle渠道配置
- **混淆配置**: 检查 `proguard-rules.pro`
- **适配问题**: 测试不同Android版本

#### HarmonyOS问题
- **API兼容**: 检查HarmonyOS API兼容性
- **权限映射**: 确认权限正确映射
- **构建配置**: 检查 `ohos/` 目录配置

## 🛠️ 维护和更新指南

### 定期维护任务
1. **依赖更新**: 每月检查并更新依赖包
2. **安全扫描**: 定期进行安全漏洞扫描
3. **性能监控**: 关注应用性能指标
4. **用户反馈**: 及时处理用户反馈问题

### 版本发布检查清单
- [ ] 代码审查完成
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 文档更新完成
- [ ] 版本号更新
- [ ] 发布说明准备

### 紧急问题处理
1. **崩溃问题**: 立即回滚到稳定版本
2. **安全漏洞**: 紧急修复并发布补丁
3. **API故障**: 启用降级方案
4. **第三方服务故障**: 切换备用服务

### 监控告警
- **崩溃率**: >1% 需要立即处理
- **网络错误率**: >5% 需要检查
- **启动时间**: >3秒需要优化
- **内存使用**: >200MB需要优化

## 📋 部署和发布

### Android发布
1. 配置签名文件
2. 设置渠道信息
3. 构建APK/AAB
4. 上传应用商店

### iOS发布
1. 配置证书和描述文件
2. 设置App Store Connect
3. 构建IPA文件
4. 提交审核

### 版本管理
- 版本号格式: `major.minor.patch+buildNumber`
- 当前版本: `1.3.3+**********`

## 📞 技术支持

### 联系方式
- **开发团队**: [团队联系方式]
- **技术文档**: 参考 `技术文档.md`
- **API文档**: [API文档地址]

### 学习资源
- [Flutter官方文档](https://flutter.dev/docs)
- [GetX文档](https://github.com/jonataslaw/getx)
- [Dio网络库](https://github.com/cfug/dio)

## 🔍 代码规范和最佳实践

### 命名规范
- **文件命名**: 使用下划线分隔 (snake_case)
- **类命名**: 使用大驼峰 (PascalCase)
- **变量命名**: 使用小驼峰 (camelCase)
- **常量命名**: 使用大写下划线 (UPPER_SNAKE_CASE)

### 目录结构规范
```
modules/[模块名]/
├── controllers/          # 控制器
├── models/              # 数据模型
├── views/               # 页面视图
└── widgets/             # 模块专用组件
```

### GetX使用规范
- 每个页面都有对应的Controller和Binding
- 使用 `Get.find()` 获取Controller实例
- 状态更新使用 `update()` 或 `.obs` 响应式变量
- 页面跳转使用 `GetRouteUtil.getToNamePage()`

### 网络请求规范
- 统一使用BaseController中的网络方法
- 错误处理通过 `appResponse.when()` 处理
- 加载状态使用 `Loading.esayLoading()` 显示

## 📊 性能优化建议

### 内存管理
- 及时释放Controller中的资源 (`onClose()` 方法)
- 图片使用 `cached_network_image` 进行缓存
- 列表使用懒加载和分页

### 网络优化
- 启用请求缓存 (`dio_cache_interceptor`)
- 图片压缩和格式优化
- API接口合并减少请求次数

### UI性能
- 使用 `flutter_screenutil` 进行屏幕适配
- 避免频繁的 `setState()` 调用
- 长列表使用 `ListView.builder()`

## 🧪 测试指南

### 单元测试
- 测试文件位置: `test/` 目录
- 运行命令: `flutter test`
- 主要测试Controller业务逻辑

### 集成测试
- 测试用户完整流程
- 包括登录、播放、聊天等核心功能

### 调试技巧
- 使用DoKit调试面板查看网络请求
- Logger输出关键信息
- 使用Flutter Inspector检查UI层级

## 🔄 版本更新流程

### 开发流程
1. 从主分支创建功能分支
2. 开发完成后提交代码审查
3. 测试通过后合并到主分支
4. 发布版本并打标签

### 版本号规则
- **主版本号**: 重大功能更新
- **次版本号**: 新功能添加
- **修订号**: Bug修复
- **构建号**: 每次构建递增

## 📋 常用命令

### Flutter命令
```bash
# 获取依赖
flutter pub get

# 清理项目
flutter clean

# 运行项目
flutter run

# 构建APK
flutter build apk

# 构建iOS
flutter build ios

# 分析代码
flutter analyze

# 格式化代码
dart format .
```

### 调试命令
```bash
# 查看设备
flutter devices

# 查看日志
flutter logs

# 性能分析
flutter run --profile
```

## 🛡️ 安全注意事项

### 敏感信息保护
- API密钥通过环境变量管理
- 用户Token加密存储
- 网络请求使用HTTPS

### 代码安全
- 避免硬编码敏感信息
- 定期更新依赖包
- 使用代码混淆保护

### 隐私合规
- 遵循各平台隐私政策
- 用户数据加密传输
- 权限申请说明清晰

## 📈 监控和分析

### 数据统计
- 神策统计: 用户行为分析
- 崩溃监控: 异常信息收集
- 性能监控: 应用性能指标

### 关键指标
- 用户活跃度
- 功能使用率
- 播放完成率
- 付费转化率

---

**注意**: 本文档会随着项目迭代持续更新，建议定期查看最新版本。

**最后更新**: 2025年7月9日
**文档版本**: v1.0
